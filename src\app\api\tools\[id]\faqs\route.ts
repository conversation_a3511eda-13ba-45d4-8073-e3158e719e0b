import { NextRequest, NextResponse } from 'next/server';
import { getToolFAQs } from '@/lib/supabase';
import { FAQFilters } from '@/lib/types';

/**
 * GET /api/tools/[id]/faqs
 * Get FAQs for a specific tool (public endpoint)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters for filtering
    const filters: Partial<FAQFilters> = {
      category: searchParams.get('category') as any || undefined,
      isFeatured: searchParams.get('featured') ? searchParams.get('featured') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      sortBy: searchParams.get('sortBy') as any || 'display_order',
      sortOrder: searchParams.get('sortOrder') as 'asc' | 'desc' || 'asc',
    };

    const faqs = await getToolFAQs(id, filters);

    return NextResponse.json({
      success: true,
      data: faqs,
    });
  } catch (error) {
    console.error('Error fetching tool FAQs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch FAQs' },
      { status: 500 }
    );
  }
}
