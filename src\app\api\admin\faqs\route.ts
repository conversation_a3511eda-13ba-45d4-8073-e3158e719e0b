import { NextRequest, NextResponse } from 'next/server';
import { getAdminFAQs, createFAQ } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';
import { FAQ, FAQFilters } from '@/lib/types';

/**
 * GET /api/admin/faqs
 * Get all FAQs for admin panel with filtering support
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: FAQFilters = {
      toolId: searchParams.get('toolId') || undefined,
      category: searchParams.get('category') as any || undefined,
      source: searchParams.get('source') as any || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      isFeatured: searchParams.get('isFeatured') ? searchParams.get('isFeatured') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
      sortBy: searchParams.get('sortBy') as any || undefined,
      sortOrder: searchParams.get('sortOrder') as 'asc' | 'desc' || undefined,
    };

    const result = await getAdminFAQs(filters);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching admin FAQs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch FAQs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/faqs
 * Create a new FAQ (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const faqData: Partial<FAQ> = await request.json();

    // Validate required fields
    if (!faqData.toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    if (!faqData.question || !faqData.question.trim()) {
      return NextResponse.json(
        { success: false, error: 'Question is required' },
        { status: 400 }
      );
    }

    if (!faqData.answer || !faqData.answer.trim()) {
      return NextResponse.json(
        { success: false, error: 'Answer is required' },
        { status: 400 }
      );
    }

    // Validate question length
    if (faqData.question.length < 5 || faqData.question.length > 500) {
      return NextResponse.json(
        { success: false, error: 'Question must be between 5 and 500 characters' },
        { status: 400 }
      );
    }

    // Validate answer length
    if (faqData.answer.length < 10 || faqData.answer.length > 5000) {
      return NextResponse.json(
        { success: false, error: 'Answer must be between 10 and 5000 characters' },
        { status: 400 }
      );
    }

    // Validate priority if provided
    if (faqData.priority !== undefined && (faqData.priority < 0 || faqData.priority > 10)) {
      return NextResponse.json(
        { success: false, error: 'Priority must be between 0 and 10' },
        { status: 400 }
      );
    }

    // Validate display order if provided
    if (faqData.displayOrder !== undefined && faqData.displayOrder < 0) {
      return NextResponse.json(
        { success: false, error: 'Display order must be 0 or greater' },
        { status: 400 }
      );
    }

    const newFAQ = await createFAQ(faqData);

    return NextResponse.json({
      success: true,
      data: newFAQ,
    });
  } catch (error) {
    console.error('Error creating FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create FAQ' },
      { status: 500 }
    );
  }
}
