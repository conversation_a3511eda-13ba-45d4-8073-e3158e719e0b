-- FAQ System Database Schema Migration
-- Creates the faqs table with proper relationships and constraints
-- Following existing database patterns and naming conventions

-- Enable necessary extensions (if not already enabled)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create faqs table
CREATE TABLE IF NOT EXISTS faqs (
  -- Primary key
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Foreign key relationship to tools table
  tool_id VARCHAR(255) NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
  
  -- FAQ content
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  
  -- Ordering and priority
  display_order INTEGER DEFAULT 0,
  priority INTEGER DEFAULT 0 CHECK (priority >= 0 AND priority <= 10),
  
  -- Categorization and metadata
  category VARCHAR(100), -- 'general', 'pricing', 'features', 'support', 'getting-started'
  tags JSONB, -- Array of tags for filtering/searching
  
  -- Status and workflow
  is_active BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  
  -- Source tracking
  source VARCHAR(50) DEFAULT 'manual', -- 'manual', 'ai_generated', 'scraped', 'user_submitted'
  source_metadata JSONB, -- Additional metadata about the source
  
  -- SEO and metadata
  meta_keywords TEXT, -- Keywords for search optimization
  help_score INTEGER DEFAULT 0, -- User helpfulness rating
  view_count INTEGER DEFAULT 0, -- Track FAQ popularity
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_faqs_tool_id ON faqs(tool_id);
CREATE INDEX IF NOT EXISTS idx_faqs_active ON faqs(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_faqs_featured ON faqs(is_featured) WHERE is_featured = TRUE;
CREATE INDEX IF NOT EXISTS idx_faqs_category ON faqs(category);
CREATE INDEX IF NOT EXISTS idx_faqs_priority ON faqs(priority DESC);
CREATE INDEX IF NOT EXISTS idx_faqs_display_order ON faqs(tool_id, display_order);
CREATE INDEX IF NOT EXISTS idx_faqs_source ON faqs(source);
CREATE INDEX IF NOT EXISTS idx_faqs_created_at ON faqs(created_at DESC);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_faqs_tool_active_order ON faqs(tool_id, is_active, display_order) WHERE is_active = TRUE;

-- Create full-text search index for questions and answers
CREATE INDEX IF NOT EXISTS idx_faqs_search ON faqs USING gin(to_tsvector('english', question || ' ' || answer));

-- Add constraints
ALTER TABLE faqs ADD CONSTRAINT chk_faqs_question_length CHECK (char_length(question) >= 5 AND char_length(question) <= 500);
ALTER TABLE faqs ADD CONSTRAINT chk_faqs_answer_length CHECK (char_length(answer) >= 10 AND char_length(answer) <= 5000);
ALTER TABLE faqs ADD CONSTRAINT chk_faqs_display_order CHECK (display_order >= 0);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_faqs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_faqs_updated_at
  BEFORE UPDATE ON faqs
  FOR EACH ROW
  EXECUTE FUNCTION update_faqs_updated_at();

-- Add RLS (Row Level Security) policies if needed
-- ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access to active FAQs
-- CREATE POLICY "Public can view active FAQs" ON faqs
--   FOR SELECT USING (is_active = TRUE);

-- Create policy for admin access
-- CREATE POLICY "Admin can manage all FAQs" ON faqs
--   FOR ALL USING (auth.role() = 'admin');

-- Insert sample FAQ data for testing (optional)
-- This can be removed in production
INSERT INTO faqs (tool_id, question, answer, category, display_order, priority, source) 
SELECT 
  t.id,
  'What is ' || t.name || '?',
  COALESCE(t.detailed_description, t.description, t.name || ' is an AI-powered tool designed to help users with various tasks.'),
  'general',
  1,
  5,
  'ai_generated'
FROM tools t 
WHERE t.content_status = 'published' 
  AND NOT EXISTS (SELECT 1 FROM faqs f WHERE f.tool_id = t.id AND f.category = 'general')
LIMIT 5; -- Only add for first 5 tools to avoid overwhelming the database

-- Add comment for documentation
COMMENT ON TABLE faqs IS 'Stores frequently asked questions for AI tools with proper categorization and ordering';
COMMENT ON COLUMN faqs.tool_id IS 'Foreign key reference to the tools table';
COMMENT ON COLUMN faqs.display_order IS 'Order in which FAQs should be displayed (0 = first)';
COMMENT ON COLUMN faqs.priority IS 'Priority level for FAQ importance (0-10, higher = more important)';
COMMENT ON COLUMN faqs.category IS 'FAQ category for grouping (general, pricing, features, support, getting-started)';
COMMENT ON COLUMN faqs.source IS 'Source of the FAQ (manual, ai_generated, scraped, user_submitted)';
COMMENT ON COLUMN faqs.help_score IS 'User helpfulness rating for the FAQ';
COMMENT ON COLUMN faqs.view_count IS 'Number of times this FAQ has been viewed';
