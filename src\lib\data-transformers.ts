/**
 * Data Transformation Utilities
 * 
 * Handles bidirectional transformation between frontend camelCase and database snake_case
 * Ensures consistent field mapping across all admin operations
 */

import { AITool, DbTool } from './types';

/**
 * Transform frontend AITool (camelCase) to database DbTool (snake_case)
 */
export function transformAIToolToDbTool(aiTool: Partial<AITool>): Partial<DbTool> {
  const dbTool: Partial<DbTool> = {};

  // Core fields
  if (aiTool.id !== undefined) dbTool.id = aiTool.id;
  if (aiTool.name !== undefined) dbTool.name = aiTool.name;
  if (aiTool.slug !== undefined) dbTool.slug = aiTool.slug;
  if (aiTool.logoUrl !== undefined) dbTool.logo_url = aiTool.logoUrl;
  if (aiTool.description !== undefined) dbTool.description = aiTool.description;
  if (aiTool.shortDescription !== undefined) dbTool.short_description = aiTool.shortDescription;
  if (aiTool.detailedDescription !== undefined) dbTool.detailed_description = aiTool.detailedDescription;
  if (aiTool.link !== undefined) dbTool.link = aiTool.link;
  if (aiTool.website !== undefined) dbTool.website = aiTool.website;
  if (aiTool.category !== undefined) dbTool.category_id = aiTool.category;
  if (aiTool.subcategory !== undefined) dbTool.subcategory = aiTool.subcategory;
  if (aiTool.company !== undefined) dbTool.company = aiTool.company;

  // Boolean fields
  if (aiTool.isVerified !== undefined) dbTool.is_verified = aiTool.isVerified;
  if (aiTool.isClaimed !== undefined) dbTool.is_claimed = aiTool.isClaimed;

  // JSONB fields
  if (aiTool.features !== undefined) dbTool.features = aiTool.features;
  if (aiTool.screenshots !== undefined) dbTool.screenshots = aiTool.screenshots;
  if (aiTool.pricing !== undefined) dbTool.pricing = aiTool.pricing;
  if (aiTool.socialLinks !== undefined) dbTool.social_links = aiTool.socialLinks;
  if (aiTool.prosAndCons !== undefined) dbTool.pros_and_cons = aiTool.prosAndCons;
  if (aiTool.haiku !== undefined) dbTool.haiku = aiTool.haiku;
  if (aiTool.hashtags !== undefined) dbTool.hashtags = aiTool.hashtags;
  if (aiTool.releases !== undefined) dbTool.releases = aiTool.releases;
  if (aiTool.claimInfo !== undefined) dbTool.claim_info = aiTool.claimInfo;

  // Metadata fields
  if (aiTool.metaTitle !== undefined) dbTool.meta_title = aiTool.metaTitle;
  if (aiTool.metaDescription !== undefined) dbTool.meta_description = aiTool.metaDescription;

  // Enhanced AI System fields
  if (aiTool.scrapedData !== undefined) dbTool.scraped_data = aiTool.scrapedData;
  if (aiTool.aiGenerationStatus !== undefined) dbTool.ai_generation_status = aiTool.aiGenerationStatus;
  if (aiTool.lastScrapedAt !== undefined) dbTool.last_scraped_at = aiTool.lastScrapedAt;
  if (aiTool.editorialReviewId !== undefined) dbTool.editorial_review_id = aiTool.editorialReviewId;
  if (aiTool.aiGenerationJobId !== undefined) dbTool.ai_generation_job_id = aiTool.aiGenerationJobId;
  if (aiTool.submissionType !== undefined) dbTool.submission_type = aiTool.submissionType;
  if (aiTool.submissionSource !== undefined) dbTool.submission_source = aiTool.submissionSource;
  if (aiTool.contentQualityScore !== undefined) dbTool.content_quality_score = aiTool.contentQualityScore;
  if (aiTool.lastAiUpdate !== undefined) dbTool.last_ai_update = aiTool.lastAiUpdate;

  // Status and workflow fields
  if (aiTool.contentStatus !== undefined) dbTool.content_status = aiTool.contentStatus;

  // Timestamp fields
  if (aiTool.createdAt !== undefined) dbTool.created_at = aiTool.createdAt;
  if (aiTool.updatedAt !== undefined) dbTool.updated_at = aiTool.updatedAt;
  if (aiTool.publishedAt !== undefined) dbTool.published_at = aiTool.publishedAt;

  return dbTool;
}

/**
 * Transform database DbTool (snake_case) to frontend AITool (camelCase)
 */
export function transformDbToolToAITool(dbTool: DbTool): AITool {
  return {
    // Core fields
    id: dbTool.id,
    name: dbTool.name,
    slug: dbTool.slug || '',
    logoUrl: dbTool.logo_url || '',
    description: dbTool.description || '',
    shortDescription: dbTool.short_description || '',
    detailedDescription: dbTool.detailed_description || '',
    link: dbTool.link,
    website: dbTool.website || '',
    category: dbTool.category_id || '',
    subcategory: dbTool.subcategory || '',
    company: dbTool.company || '',

    // Boolean fields
    isVerified: dbTool.is_verified || false,
    isClaimed: dbTool.is_claimed || false,

    // JSONB fields
    features: dbTool.features || undefined,
    screenshots: dbTool.screenshots || undefined,
    pricing: dbTool.pricing || undefined,
    socialLinks: dbTool.social_links || undefined,
    prosAndCons: dbTool.pros_and_cons || undefined,
    haiku: dbTool.haiku || undefined,
    hashtags: dbTool.hashtags || undefined,
    releases: dbTool.releases || undefined,
    claimInfo: dbTool.claim_info || undefined,

    // Metadata fields
    metaTitle: dbTool.meta_title || '',
    metaDescription: dbTool.meta_description || '',

    // Enhanced AI System fields
    scrapedData: dbTool.scraped_data || undefined,
    aiGenerationStatus: dbTool.ai_generation_status || 'pending',
    lastScrapedAt: dbTool.last_scraped_at || '',
    editorialReviewId: dbTool.editorial_review_id || '',
    aiGenerationJobId: dbTool.ai_generation_job_id || '',
    submissionType: dbTool.submission_type || 'admin',
    submissionSource: dbTool.submission_source || '',
    contentQualityScore: dbTool.content_quality_score || undefined,
    lastAiUpdate: dbTool.last_ai_update || '',

    // Status and workflow fields
    contentStatus: dbTool.content_status || 'draft',

    // Timestamp fields
    createdAt: dbTool.created_at || '',
    updatedAt: dbTool.updated_at || '',
    publishedAt: dbTool.published_at || '',
  };
}

/**
 * Transform form data (snake_case) to AITool (camelCase) for API submission
 */
export function transformFormDataToAITool(formData: any): Partial<AITool> {
  return {
    // Core fields
    name: formData.name,
    slug: formData.slug,
    logoUrl: formData.logo_url,
    description: formData.description,
    shortDescription: formData.short_description,
    detailedDescription: formData.detailed_description,
    link: formData.link,
    website: formData.website,
    category: formData.category_id, // Transform category_id to category
    subcategory: formData.subcategory,
    company: formData.company,

    // Boolean fields
    isVerified: formData.is_verified || false,
    isClaimed: formData.is_claimed || false,

    // JSONB fields - handle arrays and objects
    features: Array.isArray(formData.features) ? formData.features :
              (typeof formData.features === 'string' && formData.features.trim()) ?
              formData.features.split('\n').map((f: string) => f.trim()).filter((f: string) => f.length > 0) :
              undefined,
    screenshots: formData.screenshots?.filter((url: string) => url && url.trim()) || undefined,
    pricing: formData.pricing?.type ? {
      type: formData.pricing.type,
      plans: formData.pricing.plans?.filter((plan: any) => plan.name && plan.price) || []
    } : undefined,
    socialLinks: formData.social_links ? Object.fromEntries(
      Object.entries(formData.social_links).filter(([_, value]) => value && typeof value === 'string' && value.trim())
    ) : undefined,
    prosAndCons: (formData.pros_and_cons?.pros?.length || formData.pros_and_cons?.cons?.length) ? {
      pros: formData.pros_and_cons.pros?.filter((pro: string) => pro && pro.trim()) || [],
      cons: formData.pros_and_cons.cons?.filter((con: string) => con && con.trim()) || []
    } : undefined,
    hashtags: formData.hashtags?.filter((tag: string) => tag && tag.trim()) || undefined,
    releases: formData.releases?.filter((release: any) =>
      release.version && release.date && release.notes
    ) || undefined,

    // Metadata fields
    metaTitle: formData.meta_title,
    metaDescription: formData.meta_description,

    // Status and workflow fields
    contentStatus: formData.content_status || 'draft',
    submissionType: formData.submission_type || 'admin',
    submissionSource: formData.submission_source || 'admin_panel',
    aiGenerationStatus: formData.ai_generation_status || 'pending',
    contentQualityScore: formData.content_quality_score,

    // Timestamps
    createdAt: formData.created_at || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

/**
 * Transform AITool (camelCase) to form data (snake_case) for form initialization
 */
export function transformAIToolToFormData(aiTool: AITool): any {
  return {
    // Core fields
    name: aiTool.name,
    slug: aiTool.slug || '',
    logo_url: aiTool.logoUrl || '',
    description: aiTool.description || '',
    short_description: aiTool.shortDescription || '',
    detailed_description: aiTool.detailedDescription || '',
    link: aiTool.link,
    website: aiTool.website || '',
    category_id: aiTool.category || '', // Transform category to category_id
    subcategory: aiTool.subcategory || '',
    company: aiTool.company || '',

    // Boolean fields
    is_verified: aiTool.isVerified || false,
    is_claimed: aiTool.isClaimed || false,

    // JSONB fields
    features: Array.isArray(aiTool.features) ? aiTool.features.join('\n') : '',
    screenshots: aiTool.screenshots || [],
    pricing: aiTool.pricing || undefined,
    social_links: aiTool.socialLinks || undefined,
    pros_and_cons: aiTool.prosAndCons || undefined,
    hashtags: aiTool.hashtags || [],
    releases: aiTool.releases || [],

    // Metadata fields
    meta_title: aiTool.metaTitle || '',
    meta_description: aiTool.metaDescription || '',

    // Status and workflow fields
    content_status: aiTool.contentStatus || 'draft',
    submission_type: aiTool.submissionType || 'admin',
    submission_source: aiTool.submissionSource || 'admin_panel',
    ai_generation_status: aiTool.aiGenerationStatus || 'pending',
    content_quality_score: aiTool.contentQualityScore || undefined,
  };
}

/**
 * Validate field mappings to ensure no data loss during transformation
 */
export function validateFieldMappings(original: any, transformed: any): { isValid: boolean; missingFields: string[] } {
  const missingFields: string[] = [];

  // Define critical field mappings: original field name -> transformed field name
  const criticalFieldMappings = {
    'name': 'name',
    'description': 'description',
    'category_id': 'category', // category_id in form becomes category in AITool
    'link': 'link',
    'website': 'website'
  };

  for (const [originalField, transformedField] of Object.entries(criticalFieldMappings)) {
    // Only validate if the original field has a meaningful value (not empty string or null/undefined)
    const originalValue = original[originalField];
    const transformedValue = transformed[transformedField];

    if (originalValue && originalValue.trim && originalValue.trim() !== '') {
      // Original has a non-empty string value
      if (!transformedValue || (transformedValue.trim && transformedValue.trim() === '')) {
        missingFields.push(originalField);
      }
    } else if (originalValue && !originalValue.trim) {
      // Original has a non-string value (like boolean, number, object)
      if (transformedValue === undefined || transformedValue === null) {
        missingFields.push(originalField);
      }
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}
