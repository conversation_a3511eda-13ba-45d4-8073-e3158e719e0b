'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import { AITool, FAQ } from '@/lib/types';
import { apiClient } from '@/lib/api';

interface ToolQASectionProps {
  tool: AITool;
}

// Sample Q&A data for AI tools
const generateSampleQA = (tool: AITool) => [
  {
    id: 'what-is',
    question: `What is ${tool.name}?`,
    answer: tool.detailedDescription || tool.description || `${tool.name} is an AI-powered tool designed to help users with various tasks. It offers innovative features and capabilities that leverage artificial intelligence to improve productivity and efficiency.`
  },
  {
    id: 'how-to-use',
    question: `How do I get started with ${tool.name}?`,
    answer: `Getting started with ${tool.name} is simple. Visit their website, create an account, and follow the onboarding process. Most AI tools offer tutorials or guided tours to help new users understand the features and capabilities.`
  },
  {
    id: 'pricing',
    question: `Is ${tool.name} free to use?`,
    answer: tool.pricing?.type === 'free' 
      ? `Yes, ${tool.name} offers a free plan that includes basic features.`
      : tool.pricing?.type === 'freemium'
      ? `${tool.name} offers both free and paid plans. The free plan includes basic features, while premium plans unlock advanced capabilities.`
      : `${tool.name} is a paid service. Check their pricing page for current plans and features.`
  },
  {
    id: 'features',
    question: `What are the main features of ${tool.name}?`,
    answer: tool.features?.length 
      ? `${tool.name} offers several key features including: ${tool.features.slice(0, 3).join(', ')}${tool.features.length > 3 ? ', and more' : ''}.`
      : `${tool.name} provides AI-powered capabilities designed to enhance productivity and streamline workflows. Visit their website for a complete list of features.`
  },
  {
    id: 'support',
    question: `Does ${tool.name} offer customer support?`,
    answer: `Most AI tools, including ${tool.name}, provide customer support through various channels such as email, chat, or help documentation. Check their website for specific support options and contact information.`
  }
];

export function ToolQASection({ tool }: ToolQASectionProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load FAQs from database
  useEffect(() => {
    const loadFAQs = async () => {
      try {
        setLoading(true);
        setError(null);

        // Try to fetch FAQs from database
        const dbFaqs = await apiClient.getToolFAQs(tool.id, {
          sortBy: 'display_order',
          sortOrder: 'asc'
        });

        setFaqs(dbFaqs);
      } catch (err) {
        console.error('Failed to load FAQs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load FAQs');
        // Don't set faqs to empty array - we'll use fallback
      } finally {
        setLoading(false);
      }
    };

    if (tool.id) {
      loadFAQs();
    } else {
      setLoading(false);
    }
  }, [tool.id]);

  // Use database FAQs if available, otherwise fallback to generated ones
  const qaItems = faqs.length > 0
    ? faqs.map(faq => ({
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        category: faq.category,
        isFeatured: faq.isFeatured
      }))
    : generateSampleQA(tool).map(item => ({
        ...item,
        category: undefined,
        isFeatured: false
      }));

  const toggleItem = (id: string) => {
    setExpandedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Show loading state
  if (loading) {
    return (
      <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
            <HelpCircle size={16} className="text-white" />
          </div>
          <h3 className="text-xl font-bold text-white">Frequently Asked Questions</h3>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-400">Loading FAQs...</span>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
          <HelpCircle size={16} className="text-white" />
        </div>
        <h3 className="text-xl font-bold text-white">Frequently Asked Questions</h3>
        {faqs.length > 0 && (
          <span className="text-xs bg-green-600 text-white px-2 py-1 rounded-full">
            Database
          </span>
        )}
      </div>

      {error && faqs.length === 0 && (
        <div className="mb-4 p-3 bg-yellow-900/30 border border-yellow-600 rounded-lg">
          <p className="text-yellow-400 text-sm">
            Unable to load custom FAQs. Showing generated questions instead.
          </p>
        </div>
      )}

      <div className="space-y-4">
        {qaItems.map((item) => {
          const isExpanded = expandedItems.includes(item.id);

          return (
            <div key={item.id} className="border border-zinc-600 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-zinc-700 transition-colors duration-200"
              >
                <div className="flex items-center gap-2 flex-1">
                  <h4 className="text-white font-medium pr-4">
                    {item.question}
                  </h4>
                  {item.isFeatured && (
                    <span className="text-xs bg-orange-500 text-white px-2 py-1 rounded-full flex-shrink-0">
                      Featured
                    </span>
                  )}
                  {item.category && item.category !== 'general' && (
                    <span className="text-xs bg-zinc-600 text-gray-300 px-2 py-1 rounded-full flex-shrink-0 capitalize">
                      {item.category}
                    </span>
                  )}
                </div>
                {isExpanded ? (
                  <ChevronUp size={20} className="text-gray-400 flex-shrink-0" />
                ) : (
                  <ChevronDown size={20} className="text-gray-400 flex-shrink-0" />
                )}
              </button>

              {isExpanded && (
                <div className="px-4 pb-3 border-t border-zinc-600 bg-zinc-750">
                  <div className="pt-3">
                    <p className="text-gray-300 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div className="mt-6 pt-4 border-t border-zinc-600">
        <p className="text-gray-400 text-sm">
          Have more questions about {tool.name}? Visit their official website or contact their support team for detailed information.
          {faqs.length > 0 && (
            <span className="block mt-1 text-xs text-green-400">
              These FAQs are stored in our database and can be managed by administrators.
            </span>
          )}
        </p>
      </div>
    </section>
  );
}
